<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use App\Models\Laporan;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Feedback;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        return match ($user->role) {
            'admin' => redirect('/admin/dashboard'),
            'petugas' => redirect('/petugas/dashboard'),
            'superadmin' => redirect('/superadmin/dashboard'),
            default => redirect('/user/dashboard'),
        };
    }

    public function admin()
    {
        $user = auth()->user();

        // Jumlah laporan total & per status
        $totalLaporan = Laporan::count();
        $laporanBelumDiproses = Laporan::where('status', 'belum_diproses')->count();
        $laporanDiproses = Laporan::where('status', 'diproses')->count();
        $laporanSelesai = Laporan::where('status', 'selesai')->count();

        // Total semua akun pengguna (semua role)
        $totalSemuaAkun = User::count();
        $totalPetugas = User::where('role', 'petugas')->count();

        // Laporan per kategori
        $laporanPerKategori = Laporan::select('kategori', DB::raw('count(*) as total'))
            ->groupBy('kategori')
            ->get();

        // Laporan per bulan untuk grafik
        $laporanPerBulan = Laporan::select(
            DB::raw('MONTH(created_at) as bulan'),
            DB::raw('COUNT(*) as total')
        )
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy(DB::raw('MONTH(created_at)'))
            ->get();

        $labels = [];
        $data = [];

        foreach ($laporanPerBulan as $item) {
            $labels[] = Carbon::create()->month($item->bulan)->locale('id')->monthName;
            $data[] = $item->total;
        }

        // Feedback
        $totalFeedback = Feedback::count();
        $feedbackTerbaru = Feedback::latest()->take(5)->get();

        // Laporan terbaru & notifikasi
        $laporanTerbaru = Laporan::latest()->take(5)->get();
        $notifications = $user->notifications()->latest()->get();

        return view('dashboard.admin', [
            'title' => 'Dashboard Admin',
            'role' => $user->role,
            'notifications' => $notifications,
            'cards' => [
                ['title' => 'Total Laporan', 'desc' => "$totalLaporan laporan masuk", 'color' => 'blue', 'url' => route('laporan.index')],
                ['title' => 'Belum Diproses', 'desc' => "$laporanBelumDiproses laporan belum diproses", 'color' => 'gray', 'url' => route('laporan.index', ['status' => 'belum_diproses'])],
                ['title' => 'Diproses', 'desc' => "$laporanDiproses laporan sedang diproses", 'color' => 'yellow', 'url' => route('laporan.index', ['status' => 'diproses'])],
                ['title' => 'Selesai', 'desc' => "$laporanSelesai laporan telah selesai", 'color' => 'green', 'url' => route('laporan.index', ['status' => 'selesai'])],
                ['title' => 'Total Pengguna', 'desc' => "$totalSemuaAkun akun terdaftar", 'color' => 'red', 'url' => route('pengguna.index')],
                ['title' => 'Pembuatan Akun Petugas', 'desc' => "Buat akun petugas baru", 'color' => 'purple', 'url' => route('pengguna.create-petugas')],
                ['title' => 'Total Feedback', 'desc' => "$totalFeedback feedback diterima", 'color' => 'pink', 'url' => route('feedback.index')],
            ],

            'laporanPerKategori' => $laporanPerKategori,
            'laporanPerBulan' => $laporanPerBulan,
            'laporanTerbaru' => $laporanTerbaru,
            'feedbackTerbaru' => $feedbackTerbaru,
            'labels' => $labels,
            'data' => $data,
        ]);
    }

    public function petugas()
{
    $user = auth()->user();

    $totalLaporan = Laporan::count();
    $laporanDiproses = Laporan::where('status', 'diproses')->count();
    $laporanBelumDiproses = Laporan::where('status', 'belum_diproses')->count();
    $laporanSelesai = Laporan::where('status', 'selesai')->count();
    $totalSemuaAkun = User::count();
    $totalPetugas = User::where('role', 'petugas')->count();
    $totalFeedback = \App\Models\Feedback::count();

    $laporanPerKategori = Laporan::select('kategori', DB::raw('count(*) as total'))
        ->groupBy('kategori')
        ->get();

    $laporanPerBulan = Laporan::select(
        DB::raw('MONTH(created_at) as bulan'),
        DB::raw('count(*) as total')
    )
        ->groupBy(DB::raw('MONTH(created_at)'))
        ->orderBy(DB::raw('MONTH(created_at)'))
        ->get();

    $labels = [];
    $data = [];

    foreach ($laporanPerBulan as $item) {
        $labels[] = Carbon::create()->month($item->bulan)->locale('id')->monthName;
        $data[] = $item->total;
    }

    $laporanTerbaru = Laporan::latest()->take(5)->get();
    $feedbackTerbaru = \App\Models\Feedback::latest()->take(5)->get();
    $notifications = $user->notifications()->latest()->get();

    $cards = [
        ['title' => 'Total Laporan', 'desc' => "$totalLaporan laporan masuk", 'color' => 'blue', 'url' => route('laporan.index')],
        ['title' => 'Belum Diproses', 'desc' => "$laporanBelumDiproses laporan belum diproses", 'color' => 'gray', 'url' => route('laporan.index', ['status' => 'belum_diproses'])],
        ['title' => 'Diproses', 'desc' => "$laporanDiproses laporan sedang diproses", 'color' => 'yellow', 'url' => route('laporan.index', ['status' => 'diproses'])],
        ['title' => 'Selesai', 'desc' => "$laporanSelesai laporan telah selesai", 'color' => 'green', 'url' => route('laporan.index', ['status' => 'selesai'])],
        ['title' => 'Total Pengguna', 'desc' => "$totalSemuaAkun akun terdaftar", 'color' => 'red', 'url' => route('pengguna.index')],
        ['title' => 'Total Feedback', 'desc' => "$totalFeedback feedback diterima", 'color' => 'pink', 'url' => route('feedback.index')],
    ];

    return view('dashboard.petugas', [
        'title' => 'Dashboard Petugas',
        'role' => $user->role,
        'notifications' => $notifications,
        'cards' => $cards,
        'laporanPerKategori' => $laporanPerKategori,
        'laporanPerBulan' => $laporanPerBulan,
        'laporanTerbaru' => $laporanTerbaru,
        'feedbackTerbaru' => $feedbackTerbaru,
        'labels' => $labels,
        'data' => $data,

        'totalLaporan' => $totalLaporan,
        'belumDiproses' => $laporanBelumDiproses,
        'diproses' => $laporanDiproses,
        'selesai' => $laporanSelesai,
        'totalPengguna' => $totalSemuaAkun,
    ]);
}

    public function superadmin()
    {
        $user = auth()->user();
        $notifications = $user->notifications()->latest()->get();

        return view('dashboard.superadmin', [
            'title' => 'Dashboard Super Admin',
            'role' => $user->role,
            'notifications' => $notifications,
            'cards' => [],
        ]);
    }

    public function user()
    {
        $user = auth()->user();

        $totalLaporan = Laporan::where('user_id', $user->id)->count();
        $laporanDiproses = Laporan::where('user_id', $user->id)->where('status', 'diproses')->count();
        $laporanSelesai = Laporan::where('user_id', $user->id)->where('status', 'selesai')->count();
        $notifications = $user->notifications()->latest()->get();
        $laporans = Laporan::where('user_id', $user->id)->latest()->get();

        return view('dashboard.user', [
            'title' => 'Dashboard Warga',
            'role' => $user->role,
            'totalLaporan' => $totalLaporan,
            'laporanDiproses' => $laporanDiproses,
            'laporanSelesai' => $laporanSelesai,
            'notifications' => $notifications,
            'laporans' => $laporans,
            'cards' => [
                ['title' => 'Total Laporan', 'desc' => "$totalLaporan laporan yang Anda kirim", 'color' => 'blue', 'url' => route('user.laporan.index')],
                ['title' => 'Sedang Diproses', 'desc' => "$laporanDiproses laporan diproses", 'color' => 'yellow', 'url' => route('user.laporan.index', ['status' => 'diproses'])],
                ['title' => 'Selesai', 'desc' => "$laporanSelesai laporan selesai", 'color' => 'green', 'url' => route('user.laporan.index', ['status' => 'selesai'])],
            ],
        ]);
    }
}

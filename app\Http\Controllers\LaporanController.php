<?php

namespace App\Http\Controllers;

use App\Models\Laporan;
use App\Models\User;
use Illuminate\Http\Request;
use App\Notifications\LaporanDiproses;
use App\Notifications\LaporanSelesai;
use App\Notifications\LaporanBaru;

class LaporanController extends Controller
{
    public function index(Request $request)
    {
        $status = $request->query('status');
        $prioritas = $request->query('prioritas');
        $user = auth()->user();

        // Jika user biasa mengakses /laporan, redirect ke /user/laporan
        if ($user->role === 'user' && $request->is('laporan*')) {
            $params = array_filter(['status' => $status, 'prioritas' => $prioritas]);
            return redirect()->route('user.laporan.index', $params);
        }

        $query = Laporan::with('user');

        // Cek role user yang login
        if ($user->role === 'user') {
            // Kalau warga, hanya bisa lihat laporan miliknya sendiri
            $query->where('user_id', $user->id);
        }
        // Untuk admin, petugas, superadmin: tanpa filter user_id (lihat semua)

        // Filter berdasarkan status jika ada
        if ($status) {
            $query->where('status', $status);
        }

        // Filter berdasarkan prioritas jika ada
        if ($prioritas) {
            $query->where('prioritas', $prioritas);
        }

        // Urutkan berdasarkan prioritas (tinggi -> sedang -> rendah) lalu tanggal terbaru
        $laporans = $query->orderByRaw("FIELD(prioritas, 'tinggi', 'sedang', 'rendah')")
                          ->latest()
                          ->paginate(10);
        $notifications = $user->notifications()->latest()->get();

        return view('laporan.index', [
            'laporans' => $laporans,
            'status' => $status,
            'prioritas' => $prioritas,
            'notifications' => $notifications
        ]);
    }


    public function show(Laporan $laporan)
    {
        // Get notifications for current user
        $notifications = auth()->user()->notifications()->latest()->get();

        return view('laporan.show', [
            'laporan' => $laporan,
            'notifications' => $notifications


        ]);
    }

    /**
     * Tampilkan form proses laporan
     */
    public function prosesForm($id)
    {
        $laporan = Laporan::findOrFail($id);

        // Get notifications for current user
        $notifications = auth()->user()->notifications()->latest()->get();

        return view('laporan.proses', compact('laporan', 'notifications'));
    }

    /**
     * Proses laporan dan upload bukti awal
     */
    public function proses($id, Request $request)
    {
        $request->validate([
            'bukti_awal' => 'required|file|mimes:jpeg,jpg,png,mp4,mov|max:20480',
        ]);

        $laporan = Laporan::findOrFail($id);

        if ($laporan->status != 'belum_diproses') {
            return back()->with('error', 'Laporan sudah diproses atau selesai.');
        }

        $buktiPath = $request->file('bukti_awal')->store('bukti', 'public');

        $laporan->status = 'diproses';
        $laporan->bukti_awal = $buktiPath;
        $laporan->save();

        if ($laporan->user) {
            $laporan->user->notify(new LaporanDiproses($laporan));
        }

        return redirect()->route('laporan.index')
            ->with('success_title', 'Berhasil')
            ->with('success_message', 'Laporan berhasil ditandai sebagai sedang diproses!')
            ->with('success_type', 'laporan');
    }

    /**
     * Tampilkan form penyelesaian laporan
     */
    public function selesaiForm($id)
    {
        $laporan = Laporan::findOrFail($id);

        // Get notifications for current user
        $notifications = auth()->user()->notifications()->latest()->get();

        return view('laporan.selesai', compact('laporan', 'notifications'));
    }

    /**
     * Tandai laporan sebagai selesai dengan bukti penyelesaian
     */
    public function selesaikan(Request $request, $id)
    {
        $request->validate([
            'bukti' => 'required|image|max:2048',
        ]);

        $laporan = Laporan::findOrFail($id);
        $buktiPath = $request->file('bukti')->store('bukti', 'public');

        $laporan->status = 'selesai';
        $laporan->bukti = $buktiPath;
        $laporan->save();

        if ($laporan->user) {
            $laporan->user->notify(new LaporanSelesai($laporan));
        }

        return redirect()->route('laporan.index')
            ->with('success_title', 'Berhasil')
            ->with('success_message', 'Laporan berhasil ditandai sebagai selesai!')
            ->with('success_type', 'laporan');
    }

    /**
     * Tampilkan form buat laporan untuk user
     */
    public function create()
    {
        // Get notifications for current user
        $notifications = auth()->user()->notifications()->latest()->get();

        return view('laporan.create', compact('notifications'));
    }

    /**
     * Simpan laporan baru dari user
     */
    public function store(Request $request)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'isi' => 'required|string',
            'kategori' => 'required|string',
            'alamat' => 'required|string|max:500',
            'rt' => 'required|string|max:10',
            'rw' => 'required|string|max:10',
            'bukti' => 'nullable|file|mimes:jpeg,jpg,png,mp4,mov|max:20480',
        ]);

        $buktiPath = null;
        if ($request->hasFile('bukti')) {
            $buktiPath = $request->file('bukti')->store('bukti', 'public');
        }

        $laporan = Laporan::create([
            'user_id' => auth()->id(),
            'judul' => $request->judul,
            'isi' => $request->isi,
            'kategori' => $request->kategori,
            'alamat' => $request->alamat,
            'rt' => $request->rt,
            'rw' => $request->rw,
            'bukti' => $buktiPath,
            'status' => 'belum_diproses',
        ]);

        // Send notification to all petugas and admin
        $petugasAndAdmin = User::whereIn('role', ['petugas', 'admin'])->get();
        foreach ($petugasAndAdmin as $user) {
            $user->notify(new LaporanBaru($laporan));
        }

        return redirect()->route('user.dashboard')
            ->with('success_title', 'Berhasil')
            ->with('success_message', 'Laporan berhasil dikirim dan akan segera diproses!')
            ->with('success_type', 'laporan');
    }

    /**
     * Update prioritas laporan (hanya untuk admin dan petugas)
     */
    public function updatePrioritas(Request $request, $id)
    {
        // Hanya admin dan petugas yang bisa mengubah prioritas
        if (!in_array(auth()->user()->role, ['admin', 'petugas'])) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'prioritas' => 'required|in:rendah,sedang,tinggi'
        ]);

        $laporan = Laporan::findOrFail($id);
        $laporan->prioritas = $request->prioritas;
        $laporan->save();

        return redirect()->back()
                        ->with('success_title', 'Berhasil')
                        ->with('success_message', 'Prioritas laporan berhasil diperbarui!')
                        ->with('success_type', 'prioritas');
    }
}

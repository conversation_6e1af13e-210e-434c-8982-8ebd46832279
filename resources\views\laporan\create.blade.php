@extends('dashboard.template')

@section('title', 'Buat Laporan')

@section('content')
    <h1 class="text-2xl font-bold mb-4">Buat <PERSON></h1>

    @if ($errors->any())
        <div class="bg-red-100 text-red-700 p-3 rounded mb-4">
            @foreach ($errors->all() as $error)
                <div>{{ $error }}</div>
            @endforeach
        </div>
    @endif

    <form action="{{ route('lapor.store') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
        @csrf
        <div>
            <label for="judul" class="block font-semibold">Ju<PERSON><PERSON></label>
            <input type="text" name="judul" id="judul" class="w-full border rounded p-2" required>
        </div>
        <div>
            <label for="kategori" class="block font-semibold">Kategori</label>
            <select name="kategori" id="kategori" class="w-full border rounded p-2" required>
                <option value="">-- <PERSON><PERSON><PERSON> --</option>
                <option value="Infrastruktur">Infrastruktur</option>
                <option value="Lingkungan">Lingkungan</option>
                <option value="Keamanan">Keamanan</option>
                <option value="Lainnya">Lainnya</option>
            </select>
        </div>

        <!-- Alamat -->
        <div>
            <label for="alamat" class="block font-semibold">Alamat Lengkap</label>
            <textarea name="alamat" id="alamat" rows="2" class="w-full border rounded p-2" placeholder="Contoh: Jl. Merdeka No. 123, Kelurahan ABC" required></textarea>
        </div>

        <!-- RT dan RW -->
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label for="rt" class="block font-semibold">RT</label>
                <input type="text" name="rt" id="rt" class="w-full border rounded p-2" placeholder="Contoh: 001" maxlength="3" required>
            </div>
            <div>
                <label for="rw" class="block font-semibold">RW</label>
                <input type="text" name="rw" id="rw" class="w-full border rounded p-2" placeholder="Contoh: 005" maxlength="3" required>
            </div>
        </div>

        <!-- Google Maps Location Picker -->
        <div>
            <label class="block font-semibold mb-2">📍 Pilih Lokasi di Peta</label>
            <p class="text-sm text-gray-600 mb-3">Klik pada peta untuk menandai lokasi kejadian. Ini akan membantu petugas menemukan lokasi dengan mudah.</p>

            <!-- Map Container -->
            <div id="map" style="height: 400px; width: 100%;" class="border rounded-lg mb-3"></div>

            <!-- Coordinate Display -->
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label for="latitude" class="block text-sm font-medium text-gray-700">Latitude</label>
                    <input type="text" name="latitude" id="latitude" class="w-full border rounded p-2 bg-gray-50" readonly placeholder="Akan terisi otomatis">
                </div>
                <div>
                    <label for="longitude" class="block text-sm font-medium text-gray-700">Longitude</label>
                    <input type="text" name="longitude" id="longitude" class="w-full border rounded p-2 bg-gray-50" readonly placeholder="Akan terisi otomatis">
                </div>
            </div>

            <!-- Location Info -->
            <div id="location-info" class="mt-2 p-3 bg-blue-50 rounded-lg hidden">
                <p class="text-sm text-blue-800">
                    <span class="font-semibold">📍 Lokasi terpilih:</span>
                    <span id="selected-location">-</span>
                </p>
            </div>
        </div>

        <div>
            <label for="isi" class="block font-semibold">Isi Laporan</label>
            <textarea name="isi" id="isi" rows="5" class="w-full border rounded p-2" required></textarea>
        </div>
        <div>
            <label for="bukti" class="block font-semibold">Upload Bukti (opsional)</label>
            <input type="file" name="bukti" id="bukti" accept="image/*,video/*" class="w-full border rounded p-2">
            <p class="text-sm text-gray-500 mt-1">
                Format yang didukung: <strong>gambar (.jpg, .jpeg, .png)</strong> atau <strong>video (.mp4, .mov)</strong>.
                Maksimal ukuran 20MB.
            </p>
        </div>

        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Kirim Laporan</button>
    </form>
@endsection

@section('scripts')
<!-- Google Maps JavaScript API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initMap&libraries=geometry"
        onerror="handleGoogleMapsError()"></script>

<script>
let map;
let marker;
let geocoder;

// Error handling for Google Maps API
function handleGoogleMapsError() {
    console.error('Google Maps API failed to load');
    showMapError();
}

function showMapError() {
    const mapContainer = document.getElementById('map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div class="flex items-center justify-center h-full bg-red-50 border-2 border-red-200 rounded-lg">
                <div class="text-center p-6">
                    <svg class="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-red-800 mb-2">Google Maps Tidak Tersedia</h3>
                    <p class="text-red-600 text-sm mb-4">
                        Peta tidak dapat dimuat. Anda masih dapat mengisi alamat secara manual.
                    </p>
                    <button onclick="enableManualLocation()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">
                        Input Koordinat Manual
                    </button>
                </div>
            </div>
        `;
    }
}

function enableManualLocation() {
    document.getElementById('latitude').readOnly = false;
    document.getElementById('longitude').readOnly = false;
    document.getElementById('latitude').placeholder = 'Contoh: -6.2088';
    document.getElementById('longitude').placeholder = 'Contoh: 106.8456';
    document.getElementById('latitude').classList.remove('bg-gray-50');
    document.getElementById('longitude').classList.remove('bg-gray-50');

    // Show instruction
    const locationInfo = document.getElementById('location-info');
    locationInfo.classList.remove('hidden');
    document.getElementById('selected-location').textContent = 'Input manual diaktifkan. Masukkan koordinat latitude dan longitude.';
}

function initMap() {
    try {
        // Check if Google Maps is available
        if (typeof google === 'undefined' || !google.maps) {
            throw new Error('Google Maps API not loaded');
        }

        // Default location (Jakarta, Indonesia)
        const defaultLocation = { lat: -6.2088, lng: 106.8456 };

        // Initialize map
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 13,
            center: defaultLocation,
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
        });
    } catch (error) {
        console.error('Error initializing map:', error);
        showMapError();
        return;
    }

    // Initialize geocoder
    geocoder = new google.maps.Geocoder();

    // Try to get user's current location
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                map.setCenter(userLocation);
                map.setZoom(16);

                // Add marker at user's location
                addMarker(userLocation);
                updateLocationInfo(userLocation);
            },
            () => {
                console.log('Geolocation failed, using default location');
            }
        );
    }

    // Add click listener to map
    map.addListener('click', (event) => {
        const clickedLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
        };

        addMarker(clickedLocation);
        updateLocationInfo(clickedLocation);
    });
}

function addMarker(location) {
    // Remove existing marker
    if (marker) {
        marker.setMap(null);
    }

    // Add new marker
    marker = new google.maps.Marker({
        position: location,
        map: map,
        title: 'Lokasi Laporan',
        animation: google.maps.Animation.DROP,
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="#dc2626">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32),
            anchor: new google.maps.Point(16, 32)
        }
    });

    // Update coordinate inputs
    document.getElementById('latitude').value = location.lat.toFixed(8);
    document.getElementById('longitude').value = location.lng.toFixed(8);

    // Get address from coordinates
    geocoder.geocode({ location: location }, (results, status) => {
        if (status === 'OK' && results[0]) {
            const address = results[0].formatted_address;
            document.getElementById('selected-location').textContent = address;
            document.getElementById('location-info').classList.remove('hidden');
        }
    });
}

function updateLocationInfo(location) {
    const lat = location.lat.toFixed(6);
    const lng = location.lng.toFixed(6);

    // Show location info
    document.getElementById('location-info').classList.remove('hidden');

    // Update coordinate display
    document.getElementById('latitude').value = location.lat.toFixed(8);
    document.getElementById('longitude').value = location.lng.toFixed(8);
}

// Handle form submission
document.querySelector('form').addEventListener('submit', function(e) {
    const lat = document.getElementById('latitude').value;
    const lng = document.getElementById('longitude').value;

    if (!lat || !lng) {
        e.preventDefault();
        alert('Silakan pilih lokasi di peta atau masukkan koordinat secara manual!');
        return false;
    }

    // Validate coordinate ranges
    const latitude = parseFloat(lat);
    const longitude = parseFloat(lng);

    if (isNaN(latitude) || isNaN(longitude)) {
        e.preventDefault();
        alert('Koordinat harus berupa angka yang valid!');
        return false;
    }

    if (latitude < -90 || latitude > 90) {
        e.preventDefault();
        alert('Latitude harus antara -90 dan 90!');
        return false;
    }

    if (longitude < -180 || longitude > 180) {
        e.preventDefault();
        alert('Longitude harus antara -180 dan 180!');
        return false;
    }
});
</script>
@endsection

@extends('dashboard.template')

@section('title', 'Buat Laporan')

@section('content')
    <h1 class="text-2xl font-bold mb-4">Buat <PERSON></h1>

    @if ($errors->any())
        <div class="bg-red-100 text-red-700 p-3 rounded mb-4">
            @foreach ($errors->all() as $error)
                <div>{{ $error }}</div>
            @endforeach
        </div>
    @endif

    <form action="{{ route('lapor.store') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
        @csrf
        <div>
            <label for="judul" class="block font-semibold">Ju<PERSON><PERSON></label>
            <input type="text" name="judul" id="judul" class="w-full border rounded p-2" required>
        </div>
        <div>
            <label for="kategori" class="block font-semibold">Kategori</label>
            <select name="kategori" id="kategori" class="w-full border rounded p-2" required>
                <option value="">-- <PERSON><PERSON><PERSON> --</option>
                <option value="Infrastruktur">Infrastruktur</option>
                <option value="Lingkungan">Lingkungan</option>
                <option value="Keamanan">Keamanan</option>
                <option value="Lainnya">Lainnya</option>
            </select>
        </div>

        <!-- Alamat -->
        <div>
            <label for="alamat" class="block font-semibold">Alamat Lengkap</label>
            <textarea name="alamat" id="alamat" rows="2" class="w-full border rounded p-2" placeholder="Contoh: Jl. Merdeka No. 123, Kelurahan ABC" required></textarea>
        </div>

        <!-- RT dan RW -->
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label for="rt" class="block font-semibold">RT</label>
                <input type="text" name="rt" id="rt" class="w-full border rounded p-2" placeholder="Contoh: 001" maxlength="3" required>
            </div>
            <div>
                <label for="rw" class="block font-semibold">RW</label>
                <input type="text" name="rw" id="rw" class="w-full border rounded p-2" placeholder="Contoh: 005" maxlength="3" required>
            </div>
        </div>

        <div>
            <label for="isi" class="block font-semibold">Isi Laporan</label>
            <textarea name="isi" id="isi" rows="5" class="w-full border rounded p-2" required></textarea>
        </div>
        <div>
            <label for="bukti" class="block font-semibold">Upload Bukti (opsional)</label>
            <input type="file" name="bukti" id="bukti" accept="image/*,video/*" class="w-full border rounded p-2">
            <p class="text-sm text-gray-500 mt-1">
                Format yang didukung: <strong>gambar (.jpg, .jpeg, .png)</strong> atau <strong>video (.mp4, .mov)</strong>.
                Maksimal ukuran 20MB.
            </p>
        </div>

        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Kirim Laporan</button>
    </form>
@endsection

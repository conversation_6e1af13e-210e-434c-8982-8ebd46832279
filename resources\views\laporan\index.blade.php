@extends('dashboard.template')

@section('title', '<PERSON>ftar Laporan')

@section('content')
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">Da<PERSON><PERSON></h1>

    <!-- Back to Dashboard Button -->
    @if(auth()->user()->role === 'petugas')
        <a href="{{ route('dashboard.petugas') }}"
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow transition duration-200 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Kembali ke Dashboard
        </a>
    @elseif(auth()->user()->role === 'admin')
        <a href="{{ route('admin.dashboard') }}"
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow transition duration-200 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Kembali ke Dashboard
        </a>
    @endif
</div>

<!-- Filter Status dan Prioritas -->
<div class="mb-4 space-y-3">
    <!-- Filter Status -->
    <div class="flex flex-wrap gap-2">
        <span class="text-sm font-medium text-gray-700 self-center">Status:</span>
        <a href="{{ route('laporan.index') }}"
           class="px-3 py-1 rounded text-sm {{ !request('status') ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            Semua
        </a>
        <a href="{{ route('laporan.index', ['status' => 'belum_diproses']) }}"
           class="px-3 py-1 rounded text-sm {{ request('status') == 'belum_diproses' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            Belum Diproses
        </a>
        <a href="{{ route('laporan.index', ['status' => 'diproses']) }}"
           class="px-3 py-1 rounded text-sm {{ request('status') == 'diproses' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            Diproses
        </a>
        <a href="{{ route('laporan.index', ['status' => 'selesai']) }}"
           class="px-3 py-1 rounded text-sm {{ request('status') == 'selesai' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            Selesai
        </a>
    </div>

    @if(in_array(auth()->user()->role, ['admin', 'petugas']))
    <!-- Filter Prioritas -->
    <div class="flex flex-wrap gap-2">
        <span class="text-sm font-medium text-gray-700 self-center">Prioritas:</span>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status')])) }}"
           class="px-3 py-1 rounded text-sm {{ !request('prioritas') ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            Semua
        </a>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'tinggi'])) }}"
           class="px-3 py-1 rounded text-sm {{ request('prioritas') == 'tinggi' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            🔴 Tinggi
        </a>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'sedang'])) }}"
           class="px-3 py-1 rounded text-sm {{ request('prioritas') == 'sedang' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            🟡 Sedang
        </a>
        <a href="{{ route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'rendah'])) }}"
           class="px-3 py-1 rounded text-sm {{ request('prioritas') == 'rendah' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
            🟢 Rendah
        </a>
    </div>
    @endif
</div>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <table class="w-full">
        <thead>
            <tr class="bg-blue-600 text-white">
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">#</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Judul</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Kategori</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Lokasi</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Prioritas</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Pelapor</th>
                <th class="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">Aksi</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            @forelse ($laporans as $key => $laporan)
            <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ $key + 1 + ($laporans->currentPage() - 1) * $laporans->perPage() }}
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm font-medium text-gray-900">{{ $laporan->judul }}</div>
                    <div class="text-sm text-gray-500">{{ Str::limit($laporan->isi, 50) }}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {{ $laporan->kategori ?? 'Tidak ada kategori' }}
                    </span>
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm text-gray-900">
                        @if($laporan->alamat)
                            <div class="font-medium">{{ Str::limit($laporan->alamat, 30) }}</div>
                            <div class="text-gray-500">RT {{ $laporan->rt ?? '-' }} / RW {{ $laporan->rw ?? '-' }}</div>
                        @else
                            <span class="text-gray-400 italic">Alamat tidak tersedia</span>
                        @endif
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                        <!-- Form untuk mengubah prioritas -->
                        <form action="{{ route('laporan.prioritas', $laporan->id) }}" method="POST" class="inline-block">
                            @csrf
                            <select name="prioritas" onchange="this.form.submit()"
                                    class="text-xs font-semibold rounded-full border-0 focus:ring-2 focus:ring-blue-500
                                    @if($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                                    @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                                    @else bg-green-100 text-green-800 @endif">
                                <option value="rendah" {{ $laporan->prioritas == 'rendah' ? 'selected' : '' }}>🟢 Rendah</option>
                                <option value="sedang" {{ $laporan->prioritas == 'sedang' ? 'selected' : '' }}>🟡 Sedang</option>
                                <option value="tinggi" {{ $laporan->prioritas == 'tinggi' ? 'selected' : '' }}>🔴 Tinggi</option>
                            </select>
                        </form>
                    @else
                        <!-- Tampilan read-only untuk user -->
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @if($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                            @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                            @else bg-green-100 text-green-800 @endif">
                            @if($laporan->prioritas == 'tinggi') 🔴 Tinggi
                            @elseif($laporan->prioritas == 'sedang') 🟡 Sedang
                            @else 🟢 Rendah @endif
                        </span>
                    @endif
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    @if ($laporan->status == 'belum_diproses')
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Belum Diproses</span>
                    @elseif ($laporan->status == 'diproses')
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Diproses</span>
                    @else
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Selesai</span>
                    @endif
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8">
                            <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-white font-medium text-xs">
                                    {{ strtoupper(substr($laporan->user->name, 0, 1)) }}
                                </span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-900">{{ $laporan->user->name }}</div>
                        </div>
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div class="flex items-center justify-center space-x-2">
                        <a href="{{ route('laporan.show', $laporan->id) }}"
                           class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition duration-200">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Lihat
                        </a>

                        @if ($laporan->status == 'belum_diproses')
                            <a href="{{ route('laporan.proses', $laporan->id) }}"
                               class="inline-flex items-center px-2 py-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Proses
                            </a>
                        @elseif ($laporan->status == 'diproses')
                            <a href="{{ route('laporan.selesai', $laporan->id) }}"
                               class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Selesai
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="8" class="px-4 py-8 text-center">
                    <div class="flex flex-col items-center">
                        <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500 text-lg font-medium">Belum ada laporan.</p>
                    </div>
                </td>
            </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="mt-4">
    {{ $laporans->links() }}
</div>
@endsection

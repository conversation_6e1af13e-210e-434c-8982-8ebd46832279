@extends('dashboard.template')

@section('title', 'Detail Laporan')

@section('content')
<h1 class="text-2xl font-bold mb-6">Detail Laporan</h1>

<div class="bg-white shadow rounded p-6 space-y-4">

    <div>
        <h2 class="font-semibold text-lg mb-1">Judul</h2>
        <p class="text-gray-700">{{ $laporan->judul }}</p>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Kategori</h2>
        <p class="text-gray-700">{{ $laporan->kategori }}</p>
    </div>

    <!-- Lokasi -->
    <div>
        <h2 class="font-semibold text-lg mb-1">Lokasi</h2>
        <div class="text-gray-700">
            <p><strong>Alamat:</strong> {{ $laporan->alamat ?? 'Tidak ada alamat' }}</p>
            <p><strong>RT/RW:</strong> {{ $laporan->rt ?? '-' }} / {{ $laporan->rw ?? '-' }}</p>
        </div>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Isi Laporan</h2>
        <p class="text-gray-700">{{ $laporan->isi }}</p>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Status</h2>
        <span class="inline-block px-3 py-1 rounded text-white
            @if ($laporan->status == 'belum_diproses') bg-gray-500
            @elseif($laporan->status == 'diproses') bg-yellow-500
            @else bg-green-600 @endif">
            {{ ucfirst($laporan->status) }}
        </span>
    </div>

    <!-- Prioritas -->
    <div>
        <h2 class="font-semibold text-lg mb-1">Prioritas</h2>
        <div class="flex items-center space-x-3">
            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                @if($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                @else bg-green-100 text-green-800 @endif">
                @if($laporan->prioritas == 'tinggi') 🔴 Prioritas Tinggi
                @elseif($laporan->prioritas == 'sedang') 🟡 Prioritas Sedang
                @else 🟢 Prioritas Rendah @endif
            </span>

            @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                <!-- Form untuk mengubah prioritas -->
                <form action="{{ route('laporan.prioritas', $laporan->id) }}" method="POST" class="inline-block">
                    @csrf
                    <select name="prioritas" onchange="this.form.submit()"
                            class="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500">
                        <option value="rendah" {{ $laporan->prioritas == 'rendah' ? 'selected' : '' }}>Rendah</option>
                        <option value="sedang" {{ $laporan->prioritas == 'sedang' ? 'selected' : '' }}>Sedang</option>
                        <option value="tinggi" {{ $laporan->prioritas == 'tinggi' ? 'selected' : '' }}>Tinggi</option>
                    </select>
                </form>
            @endif
        </div>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Pelapor</h2>
        <p class="text-gray-700">{{ $laporan->user->name ?? '-' }}</p>
    </div>

    @if ($laporan->bukti_awal)
    <div>
        <h2 class="font-semibold text-lg mb-3">Bukti Kondisi Awal</h2>
        <div class="bg-gray-50 p-4 rounded-lg">
            @if (Str::endsWith($laporan->bukti_awal, ['.mp4', '.mov']))
                <video controls class="w-full max-w-md rounded-lg shadow-md">
                    <source src="{{ asset('storage/' . $laporan->bukti_awal) }}" type="video/mp4">
                    Browser tidak mendukung video.
                </video>
            @else
                <!-- Debug info
                <div class="mb-3 text-xs text-gray-600 bg-yellow-50 p-3 rounded border">
                    <strong>Debug Info:</strong><br>
                    <strong>URL:</strong> {{ asset('storage/' . $laporan->bukti_awal) }}<br>
                    <strong>File Path:</strong> {{ $laporan->bukti_awal }}<br>
                    <strong>Public Path:</strong> {{ public_path('storage/' . $laporan->bukti_awal) }}<br>
                    <strong>File Exists:</strong> {{ file_exists(public_path('storage/' . $laporan->bukti_awal)) ? 'Yes' : 'No' }}<br>
                    <strong>Storage Path Exists:</strong> {{ file_exists(storage_path('app/public/' . $laporan->bukti_awal)) ? 'Yes' : 'No' }}
                </div> -->

                <div class="relative">
                    <img src="{{ asset('storage/' . $laporan->bukti_awal) }}"
                         alt="Bukti Kondisi Awal"
                         class="w-full max-w-md rounded-lg shadow-md border"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">

                    <!-- Fallback when image fails to load -->
                    <div class="hidden w-full max-w-md h-64 bg-gray-100 rounded-lg shadow-md border flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-sm">Gambar tidak dapat dimuat</p>
                            <p class="text-xs text-gray-400 mt-1">{{ basename($laporan->bukti_awal) }}</p>
                        </div>
                    </div>
                </div>
            @endif
            <div class="mt-3 flex items-center justify-between">
                <span class="text-sm text-gray-600">{{ basename($laporan->bukti_awal) }}</span>
                <a href="{{ asset('storage/' . $laporan->bukti_awal) }}"
                   download
                   class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download
                </a>
            </div>
        </div>
    </div>
    @endif

    @if ($laporan->bukti)
    <div>
        <h2 class="font-semibold text-lg mb-3">
            @if (auth()->user()->role == 'user')
                Bukti Penyelesaian
            @else
                Bukti Laporan
            @endif
        </h2>

        <div class="bg-gray-50 p-4 rounded-lg">
            <!-- Debug info -->
            <!-- <div class="mb-3 text-xs text-gray-600 bg-yellow-50 p-3 rounded border">
                <strong>Debug Info:</strong><br>
                <strong>URL:</strong> {{ asset('storage/' . $laporan->bukti) }}<br>
                <strong>File Path:</strong> {{ $laporan->bukti }}<br>
                <strong>Public Path:</strong> {{ public_path('storage/' . $laporan->bukti) }}<br>
                <strong>File Exists:</strong> {{ file_exists(public_path('storage/' . $laporan->bukti)) ? 'Yes' : 'No' }}<br>
                <strong>Storage Path Exists:</strong> {{ file_exists(storage_path('app/public/' . $laporan->bukti)) ? 'Yes' : 'No' }}
            </div> -->

            <div class="relative">
                <img src="{{ asset('storage/' . $laporan->bukti) }}"
                     alt="Bukti {{ auth()->user()->role == 'user' ? 'Penyelesaian' : 'Laporan' }}"
                     class="w-full max-w-md rounded-lg shadow-md"
                     onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA2MEgxNDBWMTQwSDYwVjYwWiIgZmlsbD0iI0Q1RDlERCIvPgo8cGF0aCBkPSJNODAgODBIMTIwVjEyMEg4MFY4MFoiIGZpbGw9IiNBN0E5QUMiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTZMMTMuMjkyOSA2LjcwNzExQzEzLjY4MzQgNi4zMTY1OCAxNC4zMTY2IDYuMzE2NTggMTQuNzA3MSA2LjcwNzExTDIwIDEyTTE0IDEwVjhBMiAyIDAgMCAwIDEyIDZIOEE2IDYgMCAwIDAgMiAxMlYxNkE2IDYgMCAwIDAgOCAyMkgxNkE2IDYgMCAwIDAgMjIgMTZWMTJBMiAyIDAgMCAwIDIwIDEwSDE0WiIgc3Ryb2tlPSIjNjU3Mzg4IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+'; this.classList.add('opacity-50');">
                @if (!file_exists(public_path('storage/' . $laporan->bukti)))
                    <div class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                        <div class="text-center text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-sm">Gambar tidak ditemukan</p>
                        </div>
                    </div>
                @endif
            </div>
            <div class="mt-3 flex items-center justify-between">
                <span class="text-sm text-gray-600">{{ basename($laporan->bukti) }}</span>
                <a href="{{ asset('storage/' . $laporan->bukti) }}"
                   download
                   class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download
                </a>
            </div>
        </div>
    </div>
    @endif


    <div class="mt-6">
        <a href="{{ url()->previous() }}" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">← Kembali</a>
    </div>

    @if(auth()->user()->role == 'user' && $laporan->status == 'selesai' && !$laporan->rating)
<div class="mt-6 bg-white shadow rounded p-6">
    <h2 class="font-semibold text-lg mb-4">Beri Rating Laporan Ini</h2>
    <form action="{{ route('rating.store') }}" method="POST" class="space-y-4">
        @csrf
        <input type="hidden" name="laporan_id" value="{{ $laporan->id }}">

        <div>
            <label for="rating" class="block font-medium mb-1">Rating (1 - 5 Bintang)</label>
            <select name="rating" required class="w-full border rounded px-3 py-2">
                <option value="">Pilih Bintang</option>
                <option value="1">1 - Sangat Buruk</option>
                <option value="2">2 - Buruk</option>
                <option value="3">3 - Cukup</option>
                <option value="4">4 - Baik</option>
                <option value="5">5 - Sangat Baik</option>
            </select>
        </div>

        <div>
            <label for="komentar" class="block font-medium mb-1">Komentar (Opsional)</label>
            <textarea name="komentar" rows="3" class="w-full border rounded px-3 py-2" placeholder="Tulis komentar Anda..."></textarea>
        </div>

        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Kirim Rating</button>
    </form>
</div>
@endif


</div>
@endsection

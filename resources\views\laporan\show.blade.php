@extends('dashboard.template')

@section('title', 'Detail Laporan')

@section('content')
<h1 class="text-2xl font-bold mb-6">Detail Laporan</h1>

<div class="bg-white shadow rounded p-6 space-y-4">

    <div>
        <h2 class="font-semibold text-lg mb-1">Judul</h2>
        <p class="text-gray-700">{{ $laporan->judul }}</p>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Kategori</h2>
        <p class="text-gray-700">{{ $laporan->kategori }}</p>
    </div>

    <!-- Lokasi -->
    <div>
        <h2 class="font-semibold text-lg mb-3">📍 Lokasi</h2>
        <div class="text-gray-700 space-y-3">
            <div>
                <p><strong>Alamat:</strong> {{ $laporan->alamat ?? 'Tidak ada alamat' }}</p>
                <p><strong>RT/RW:</strong> {{ $laporan->rt ?? '-' }} / {{ $laporan->rw ?? '-' }}</p>
            </div>

            @if($laporan->latitude && $laporan->longitude)
                <!-- Peta Lokasi -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-medium text-gray-800">🗺️ Peta Lokasi</h3>
                        @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                            <a href="https://www.google.com/maps/dir/?api=1&destination={{ $laporan->latitude }},{{ $laporan->longitude }}"
                               target="_blank"
                               class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition duration-200">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                                </svg>
                                Navigasi ke Lokasi
                            </a>
                        @endif
                    </div>

                    <!-- Map Container -->
                    <div id="detail-map" style="height: 300px; width: 100%;" class="border rounded-lg"></div>

                    <!-- Koordinat Info -->
                    <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-white p-2 rounded border">
                            <span class="text-gray-600">Latitude:</span>
                            <span class="font-mono">{{ number_format($laporan->latitude, 6) }}</span>
                        </div>
                        <div class="bg-white p-2 rounded border">
                            <span class="text-gray-600">Longitude:</span>
                            <span class="font-mono">{{ number_format($laporan->longitude, 6) }}</span>
                        </div>
                    </div>
                </div>
            @else
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <p class="text-yellow-800 text-sm">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        Koordinat lokasi tidak tersedia untuk laporan ini.
                    </p>
                </div>
            @endif
        </div>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Isi Laporan</h2>
        <p class="text-gray-700">{{ $laporan->isi }}</p>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Status</h2>
        <span class="inline-block px-3 py-1 rounded text-white
            @if ($laporan->status == 'belum_diproses') bg-gray-500
            @elseif($laporan->status == 'diproses') bg-yellow-500
            @else bg-green-600 @endif">
            {{ ucfirst($laporan->status) }}
        </span>
    </div>

    <!-- Prioritas -->
    <div>
        <h2 class="font-semibold text-lg mb-1">Prioritas</h2>
        <div class="flex items-center space-x-3">
            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                @if($laporan->prioritas == 'tinggi') bg-red-100 text-red-800
                @elseif($laporan->prioritas == 'sedang') bg-yellow-100 text-yellow-800
                @else bg-green-100 text-green-800 @endif">
                @if($laporan->prioritas == 'tinggi') 🔴 Prioritas Tinggi
                @elseif($laporan->prioritas == 'sedang') 🟡 Prioritas Sedang
                @else 🟢 Prioritas Rendah @endif
            </span>

            @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                <!-- Form untuk mengubah prioritas -->
                <form action="{{ route('laporan.prioritas', $laporan->id) }}" method="POST" class="inline-block">
                    @csrf
                    <select name="prioritas" onchange="this.form.submit()"
                            class="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500">
                        <option value="rendah" {{ $laporan->prioritas == 'rendah' ? 'selected' : '' }}>Rendah</option>
                        <option value="sedang" {{ $laporan->prioritas == 'sedang' ? 'selected' : '' }}>Sedang</option>
                        <option value="tinggi" {{ $laporan->prioritas == 'tinggi' ? 'selected' : '' }}>Tinggi</option>
                    </select>
                </form>
            @endif
        </div>
    </div>

    <div>
        <h2 class="font-semibold text-lg mb-1">Pelapor</h2>
        <p class="text-gray-700">{{ $laporan->user->name ?? '-' }}</p>
    </div>

    @if ($laporan->bukti_awal)
    <div>
        <h2 class="font-semibold text-lg mb-3">Bukti Kondisi Awal</h2>
        <div class="bg-gray-50 p-4 rounded-lg">
            @if (Str::endsWith($laporan->bukti_awal, ['.mp4', '.mov']))
                <video controls class="w-full max-w-md rounded-lg shadow-md">
                    <source src="{{ asset('storage/' . $laporan->bukti_awal) }}" type="video/mp4">
                    Browser tidak mendukung video.
                </video>
            @else
                <!-- Debug info
                <div class="mb-3 text-xs text-gray-600 bg-yellow-50 p-3 rounded border">
                    <strong>Debug Info:</strong><br>
                    <strong>URL:</strong> {{ asset('storage/' . $laporan->bukti_awal) }}<br>
                    <strong>File Path:</strong> {{ $laporan->bukti_awal }}<br>
                    <strong>Public Path:</strong> {{ public_path('storage/' . $laporan->bukti_awal) }}<br>
                    <strong>File Exists:</strong> {{ file_exists(public_path('storage/' . $laporan->bukti_awal)) ? 'Yes' : 'No' }}<br>
                    <strong>Storage Path Exists:</strong> {{ file_exists(storage_path('app/public/' . $laporan->bukti_awal)) ? 'Yes' : 'No' }}
                </div> -->

                <div class="relative">
                    <img src="{{ asset('storage/' . $laporan->bukti_awal) }}"
                         alt="Bukti Kondisi Awal"
                         class="w-full max-w-md rounded-lg shadow-md border"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">

                    <!-- Fallback when image fails to load -->
                    <div class="hidden w-full max-w-md h-64 bg-gray-100 rounded-lg shadow-md border flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-sm">Gambar tidak dapat dimuat</p>
                            <p class="text-xs text-gray-400 mt-1">{{ basename($laporan->bukti_awal) }}</p>
                        </div>
                    </div>
                </div>
            @endif
            <div class="mt-3 flex items-center justify-between">
                <span class="text-sm text-gray-600">{{ basename($laporan->bukti_awal) }}</span>
                <a href="{{ asset('storage/' . $laporan->bukti_awal) }}"
                   download
                   class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download
                </a>
            </div>
        </div>
    </div>
    @endif

    @if ($laporan->bukti)
    <div>
        <h2 class="font-semibold text-lg mb-3">
            @if (auth()->user()->role == 'user')
                Bukti Penyelesaian
            @else
                Bukti Laporan
            @endif
        </h2>

        <div class="bg-gray-50 p-4 rounded-lg">
            <!-- Debug info -->
            <!-- <div class="mb-3 text-xs text-gray-600 bg-yellow-50 p-3 rounded border">
                <strong>Debug Info:</strong><br>
                <strong>URL:</strong> {{ asset('storage/' . $laporan->bukti) }}<br>
                <strong>File Path:</strong> {{ $laporan->bukti }}<br>
                <strong>Public Path:</strong> {{ public_path('storage/' . $laporan->bukti) }}<br>
                <strong>File Exists:</strong> {{ file_exists(public_path('storage/' . $laporan->bukti)) ? 'Yes' : 'No' }}<br>
                <strong>Storage Path Exists:</strong> {{ file_exists(storage_path('app/public/' . $laporan->bukti)) ? 'Yes' : 'No' }}
            </div> -->

            <div class="relative">
                <img src="{{ asset('storage/' . $laporan->bukti) }}"
                     alt="Bukti {{ auth()->user()->role == 'user' ? 'Penyelesaian' : 'Laporan' }}"
                     class="w-full max-w-md rounded-lg shadow-md"
                     onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA2MEgxNDBWMTQwSDYwVjYwWiIgZmlsbD0iI0Q1RDlERCIvPgo8cGF0aCBkPSJNODAgODBIMTIwVjEyMEg4MFY4MFoiIGZpbGw9IiNBN0E5QUMiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTZMMTMuMjkyOSA2LjcwNzExQzEzLjY4MzQgNi4zMTY1OCAxNC4zMTY2IDYuMzE2NTggMTQuNzA3MSA2LjcwNzExTDIwIDEyTTE0IDEwVjhBMiAyIDAgMCAwIDEyIDZIOEE2IDYgMCAwIDAgMiAxMlYxNkE2IDYgMCAwIDAgOCAyMkgxNkE2IDYgMCAwIDAgMjIgMTZWMTJBMiAyIDAgMCAwIDIwIDEwSDE0WiIgc3Ryb2tlPSIjNjU3Mzg4IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+'; this.classList.add('opacity-50');">
                @if (!file_exists(public_path('storage/' . $laporan->bukti)))
                    <div class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                        <div class="text-center text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-sm">Gambar tidak ditemukan</p>
                        </div>
                    </div>
                @endif
            </div>
            <div class="mt-3 flex items-center justify-between">
                <span class="text-sm text-gray-600">{{ basename($laporan->bukti) }}</span>
                <a href="{{ asset('storage/' . $laporan->bukti) }}"
                   download
                   class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download
                </a>
            </div>
        </div>
    </div>
    @endif


    <div class="mt-6">
        <a href="{{ url()->previous() }}" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">← Kembali</a>
    </div>

    @if(auth()->user()->role == 'user' && $laporan->status == 'selesai' && !$laporan->rating)
<div class="mt-6 bg-white shadow rounded p-6">
    <h2 class="font-semibold text-lg mb-4">Beri Rating Laporan Ini</h2>
    <form action="{{ route('rating.store') }}" method="POST" class="space-y-4">
        @csrf
        <input type="hidden" name="laporan_id" value="{{ $laporan->id }}">

        <div>
            <label for="rating" class="block font-medium mb-1">Rating (1 - 5 Bintang)</label>
            <select name="rating" required class="w-full border rounded px-3 py-2">
                <option value="">Pilih Bintang</option>
                <option value="1">1 - Sangat Buruk</option>
                <option value="2">2 - Buruk</option>
                <option value="3">3 - Cukup</option>
                <option value="4">4 - Baik</option>
                <option value="5">5 - Sangat Baik</option>
            </select>
        </div>

        <div>
            <label for="komentar" class="block font-medium mb-1">Komentar (Opsional)</label>
            <textarea name="komentar" rows="3" class="w-full border rounded px-3 py-2" placeholder="Tulis komentar Anda..."></textarea>
        </div>

        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Kirim Rating</button>
    </form>
</div>
@endif


</div>
@endsection

@if($laporan->latitude && $laporan->longitude)
@section('scripts')
<!-- Google Maps JavaScript API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initDetailMap&libraries=geometry"
        onerror="handleDetailMapError()"></script>

<script>
let detailMap;
let detailMarker;

// Error handling for Google Maps API
function handleDetailMapError() {
    console.error('Google Maps API failed to load');
    showDetailMapError();
}

function showDetailMapError() {
    const mapContainer = document.getElementById('detail-map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div class="flex items-center justify-center h-full bg-yellow-50 border-2 border-yellow-200 rounded-lg">
                <div class="text-center p-6">
                    <svg class="w-12 h-12 text-yellow-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-yellow-800 mb-2">Peta Tidak Tersedia</h3>
                    <p class="text-yellow-600 text-sm mb-4">
                        Google Maps tidak dapat dimuat, tetapi koordinat lokasi tetap tersedia.
                    </p>
                    <div class="text-sm text-yellow-700">
                        <p><strong>Koordinat:</strong> {{ number_format($laporan->latitude, 6) }}, {{ number_format($laporan->longitude, 6) }}</p>
                        @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                            <a href="https://www.google.com/maps/dir/?api=1&destination={{ $laporan->latitude }},{{ $laporan->longitude }}"
                               target="_blank"
                               class="inline-block mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">
                                🧭 Buka di Google Maps
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        `;
    }
}

function initDetailMap() {
    try {
        // Check if Google Maps is available
        if (typeof google === 'undefined' || !google.maps) {
            throw new Error('Google Maps API not loaded');
        }

        // Koordinat laporan
        const reportLocation = {
            lat: {{ $laporan->latitude }},
            lng: {{ $laporan->longitude }}
        };

        // Initialize map
        detailMap = new google.maps.Map(document.getElementById('detail-map'), {
            zoom: 16,
            center: reportLocation,
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            zoomControl: true,
            mapTypeId: google.maps.MapTypeId.ROADMAP
        });
    } catch (error) {
        console.error('Error initializing detail map:', error);
        showDetailMapError();
        return;
    }

    // Add marker for report location
    detailMarker = new google.maps.Marker({
        position: reportLocation,
        map: detailMap,
        title: 'Lokasi Laporan: {{ $laporan->judul }}',
        animation: google.maps.Animation.DROP,
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="#dc2626">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(40, 40),
            anchor: new google.maps.Point(20, 40)
        }
    });

    // Info window with report details
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="max-width: 250px; padding: 10px;">
                <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: bold; color: #1f2937;">
                    📍 {{ $laporan->judul }}
                </h3>
                <div style="font-size: 14px; color: #4b5563; line-height: 1.4;">
                    <p style="margin: 4px 0;"><strong>Alamat:</strong> {{ $laporan->alamat ?? 'Tidak ada alamat' }}</p>
                    <p style="margin: 4px 0;"><strong>RT/RW:</strong> {{ $laporan->rt ?? '-' }}/{{ $laporan->rw ?? '-' }}</p>
                    <p style="margin: 4px 0;"><strong>Status:</strong>
                        <span style="padding: 2px 6px; border-radius: 4px; font-size: 12px; font-weight: bold; color: white;
                            background-color: {{ $laporan->status == 'belum_diproses' ? '#6b7280' : ($laporan->status == 'diproses' ? '#eab308' : '#16a34a') }};">
                            {{ ucfirst($laporan->status) }}
                        </span>
                    </p>
                    <p style="margin: 8px 0 4px 0; font-size: 12px; color: #6b7280;">
                        Koordinat: {{ number_format($laporan->latitude, 6) }}, {{ number_format($laporan->longitude, 6) }}
                    </p>
                    @if(in_array(auth()->user()->role, ['admin', 'petugas']))
                        <div style="margin-top: 10px; text-align: center;">
                            <a href="https://www.google.com/maps/dir/?api=1&destination={{ $laporan->latitude }},{{ $laporan->longitude }}"
                               target="_blank"
                               style="display: inline-block; padding: 6px 12px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 6px; font-size: 12px; font-weight: bold;">
                                🧭 Navigasi ke Lokasi
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        `
    });

    // Show info window when marker is clicked
    detailMarker.addListener('click', () => {
        infoWindow.open(detailMap, detailMarker);
    });

    // Auto-open info window after a short delay
    setTimeout(() => {
        infoWindow.open(detailMap, detailMarker);
    }, 1000);
}

// Handle map resize when container changes
window.addEventListener('resize', () => {
    if (detailMap) {
        google.maps.event.trigger(detailMap, 'resize');
        detailMap.setCenter({
            lat: {{ $laporan->latitude }},
            lng: {{ $laporan->longitude }}
        });
    }
});
</script>
@endsection
@endif

<?php $__env->startSection('title', '<PERSON><PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">Detail Laporan</h1>
        <a href="<?php echo e(url()->previous()); ?>"
           class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg shadow transition duration-200 text-sm sm:text-base">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Kembali
        </a>
    </div>

    <!-- Main Content -->
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-4 sm:p-6 lg:p-8 space-y-6">

        <!-- Title Section -->
        <div class="border-b border-gray-200 dark:border-gray-600 pb-4">
            <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-2">📋 Judul Laporan</h2>
            <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base"><?php echo e($laporan->judul); ?></p>
        </div>

        <!-- Category Section -->
        <div class="border-b border-gray-200 dark:border-gray-600 pb-4">
            <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-2">🏷️ Kategori</h2>
            <span class="inline-flex px-3 py-1 text-xs sm:text-sm font-semibold rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                <?php echo e($laporan->kategori); ?>

            </span>
        </div>

        <!-- Location Section -->
        <div class="border-b border-gray-200 dark:border-gray-600 pb-6">
            <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-4">📍 Lokasi</h2>

            <!-- Address Info -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-600 dark:text-gray-400">Alamat:</span>
                        <p class="text-gray-800 dark:text-gray-200 mt-1"><?php echo e($laporan->alamat ?? 'Tidak ada alamat'); ?></p>
                    </div>
                    <div>
                        <span class="font-medium text-gray-600 dark:text-gray-400">RT/RW:</span>
                        <p class="text-gray-800 dark:text-gray-200 mt-1"><?php echo e($laporan->rt ?? '-'); ?> / <?php echo e($laporan->rw ?? '-'); ?></p>
                    </div>
                </div>
            </div>

            <?php if($laporan->latitude && $laporan->longitude): ?>
                <!-- Map Section -->
                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 sm:p-6">
                    <!-- Map Header -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                            </svg>
                            Peta Lokasi
                        </h3>

                        <!-- Navigation Buttons -->
                        <div class="flex flex-col sm:flex-row gap-2">
                            <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                                <a href="https://www.google.com/maps/dir/?api=1&destination=<?php echo e($laporan->latitude); ?>,<?php echo e($laporan->longitude); ?>"
                                   target="_blank"
                                   class="inline-flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs sm:text-sm rounded-lg transition duration-200 shadow">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                    Navigasi
                                </a>
                            <?php endif; ?>
                            <a href="https://www.google.com/maps/search/?api=1&query=<?php echo e($laporan->latitude); ?>,<?php echo e($laporan->longitude); ?>"
                               target="_blank"
                               class="inline-flex items-center justify-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-xs sm:text-sm rounded-lg transition duration-200 shadow">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                Buka Maps
                            </a>
                        </div>
                    </div>

                    <!-- Map Container -->
                    <div class="relative">
                        <div id="detail-map" class="w-full h-64 sm:h-80 lg:h-96 rounded-lg border border-gray-200 dark:border-gray-600 shadow-inner"></div>
                        <!-- Loading overlay -->
                        <div id="map-loading" class="absolute inset-0 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                            <div class="text-center">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Memuat peta...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Coordinates Info -->
                    <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Latitude</div>
                            <div class="font-mono text-sm text-gray-800 dark:text-gray-200"><?php echo e(number_format($laporan->latitude, 6)); ?></div>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Longitude</div>
                            <div class="font-mono text-sm text-gray-800 dark:text-gray-200"><?php echo e(number_format($laporan->longitude, 6)); ?></div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- No Coordinates Available -->
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Koordinat Tidak Tersedia</h4>
                            <p class="text-yellow-700 dark:text-yellow-300 text-sm">
                                Koordinat lokasi tidak tersedia untuk laporan ini. Hanya informasi alamat yang dapat ditampilkan.
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Content Section -->
        <div class="border-b border-gray-200 dark:border-gray-600 pb-6">
            <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-3">📝 Isi Laporan</h2>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base leading-relaxed"><?php echo e($laporan->isi); ?></p>
            </div>
        </div>

        <!-- Status and Priority Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 border-b border-gray-200 dark:border-gray-600 pb-6">
            <!-- Status -->
            <div>
                <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-3">📊 Status</h2>
                <div class="flex items-center">
                    <?php if($laporan->status == 'belum_diproses'): ?>
                        <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            ⏳ Belum Diproses
                        </span>
                    <?php elseif($laporan->status == 'diproses'): ?>
                        <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            🔄 Sedang Diproses
                        </span>
                    <?php else: ?>
                        <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            ✅ Selesai
                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Priority -->
            <div>
                <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-3">🎯 Prioritas</h2>
                <div class="flex flex-col sm:flex-row sm:items-center gap-3">
                    <span class="inline-flex items-center px-3 py-2 text-sm font-semibold rounded-lg
                        <?php if($laporan->prioritas == 'tinggi'): ?> bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300
                        <?php elseif($laporan->prioritas == 'sedang'): ?> bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300
                        <?php else: ?> bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 <?php endif; ?>">
                        <?php if($laporan->prioritas == 'tinggi'): ?>
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            </svg>
                            🔴 Prioritas Tinggi
                        <?php elseif($laporan->prioritas == 'sedang'): ?>
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            🟡 Prioritas Sedang
                        <?php else: ?>
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            🟢 Prioritas Rendah
                        <?php endif; ?>
                    </span>

                    <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                        <form action="<?php echo e(route('laporan.prioritas', $laporan->id)); ?>" method="POST" class="inline-block">
                            <?php echo csrf_field(); ?>
                            <select name="prioritas" onchange="this.form.submit()"
                                    class="text-xs sm:text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="rendah" <?php echo e($laporan->prioritas == 'rendah' ? 'selected' : ''); ?>>🟢 Rendah</option>
                                <option value="sedang" <?php echo e($laporan->prioritas == 'sedang' ? 'selected' : ''); ?>>🟡 Sedang</option>
                                <option value="tinggi" <?php echo e($laporan->prioritas == 'tinggi' ? 'selected' : ''); ?>>🔴 Tinggi</option>
                            </select>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Reporter Section -->
        <div class="border-b border-gray-200 dark:border-gray-600 pb-6">
            <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-3">👤 Pelapor</h2>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base"><?php echo e($laporan->user->name ?? 'Tidak diketahui'); ?></p>
            </div>
        </div>

        <!-- Evidence Section - Initial -->
        <?php if($laporan->bukti_awal): ?>
        <div class="border-b border-gray-200 dark:border-gray-600 pb-6">
            <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-4">📸 Bukti Kondisi Awal</h2>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 sm:p-6">
                <?php if(Str::endsWith($laporan->bukti_awal, ['.mp4', '.mov'])): ?>
                    <!-- Video Evidence -->
                    <div class="relative">
                        <video controls class="w-full max-w-lg rounded-lg shadow-md border border-gray-200 dark:border-gray-600">
                            <source src="<?php echo e(asset('storage/' . $laporan->bukti_awal)); ?>" type="video/mp4">
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-8 text-center">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-gray-500 dark:text-gray-400">Browser tidak mendukung video</p>
                            </div>
                        </video>
                    </div>
                <?php else: ?>
                    <!-- Image Evidence -->
                    <div class="relative">
                        <img src="<?php echo e(asset('storage/' . $laporan->bukti_awal)); ?>"
                             alt="Bukti Kondisi Awal"
                             class="w-full max-w-lg rounded-lg shadow-md border border-gray-200 dark:border-gray-600"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">

                        <!-- Fallback when image fails to load -->
                        <div class="hidden w-full max-w-lg h-64 bg-gray-100 dark:bg-gray-700 rounded-lg shadow-md border border-gray-200 dark:border-gray-600 flex items-center justify-center">
                            <div class="text-center text-gray-500 dark:text-gray-400">
                                <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-sm">Gambar tidak dapat dimuat</p>
                                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1"><?php echo e(basename($laporan->bukti_awal)); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- File Info and Download -->
                <div class="mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-600 dark:text-gray-400"><?php echo e(basename($laporan->bukti_awal)); ?></span>
                    </div>
                    <a href="<?php echo e(asset('storage/' . $laporan->bukti_awal)); ?>"
                       download
                       class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition duration-200 shadow">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Evidence Section - Final -->
        <?php if($laporan->bukti): ?>
        <div class="border-b border-gray-200 dark:border-gray-600 pb-6">
            <h2 class="font-semibold text-base sm:text-lg text-gray-800 dark:text-gray-200 mb-4">
                <?php if(auth()->user()->role == 'user'): ?>
                    📋 Bukti Penyelesaian
                <?php else: ?>
                    📋 Bukti Laporan
                <?php endif; ?>
            </h2>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 sm:p-6">
                <div class="relative">
                    <img src="<?php echo e(asset('storage/' . $laporan->bukti)); ?>"
                         alt="Bukti <?php echo e(auth()->user()->role == 'user' ? 'Penyelesaian' : 'Laporan'); ?>"
                         class="w-full max-w-lg rounded-lg shadow-md border border-gray-200 dark:border-gray-600"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">

                    <!-- Fallback when image fails to load -->
                    <div class="hidden w-full max-w-lg h-64 bg-gray-100 dark:bg-gray-700 rounded-lg shadow-md border border-gray-200 dark:border-gray-600 flex items-center justify-center">
                        <div class="text-center text-gray-500 dark:text-gray-400">
                            <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-sm">Gambar tidak dapat dimuat</p>
                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1"><?php echo e(basename($laporan->bukti)); ?></p>
                        </div>
                    </div>
                </div>

                <!-- File Info and Download -->
                <div class="mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-600 dark:text-gray-400"><?php echo e(basename($laporan->bukti)); ?></span>
                    </div>
                    <a href="<?php echo e(asset('storage/' . $laporan->bukti)); ?>"
                       download
                       class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition duration-200 shadow">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Rating Section -->
        <?php if(auth()->user()->role == 'user' && $laporan->status == 'selesai' && !$laporan->rating): ?>
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 sm:p-6 border border-blue-200 dark:border-blue-800">
            <div class="flex items-center mb-4">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
                <h2 class="font-semibold text-lg text-gray-800 dark:text-gray-200">Beri Rating Laporan Ini</h2>
            </div>
            <p class="text-gray-600 dark:text-gray-400 text-sm mb-6">Bantu kami meningkatkan layanan dengan memberikan rating dan feedback Anda</p>

            <form action="<?php echo e(route('rating.store')); ?>" method="POST" class="space-y-4">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="laporan_id" value="<?php echo e($laporan->id); ?>">

                <div>
                    <label for="rating" class="block font-medium text-gray-700 dark:text-gray-300 mb-2">Rating (1 - 5 Bintang)</label>
                    <select name="rating" required class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Pilih Rating</option>
                        <option value="1">⭐ 1 - Sangat Buruk</option>
                        <option value="2">⭐⭐ 2 - Buruk</option>
                        <option value="3">⭐⭐⭐ 3 - Cukup</option>
                        <option value="4">⭐⭐⭐⭐ 4 - Baik</option>
                        <option value="5">⭐⭐⭐⭐⭐ 5 - Sangat Baik</option>
                    </select>
                </div>

                <div>
                    <label for="komentar" class="block font-medium text-gray-700 dark:text-gray-300 mb-2">Komentar (Opsional)</label>
                    <textarea name="komentar" rows="3"
                              class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Tulis komentar atau saran Anda..."></textarea>
                </div>

                <button type="submit"
                        class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200 shadow">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    Kirim Rating
                </button>
            </form>
        </div>
        <?php endif; ?>

    </div>
</div>


</div>
<?php $__env->stopSection(); ?>

<?php if($laporan->latitude && $laporan->longitude): ?>
<?php $__env->startSection('scripts'); ?>
<!-- Check if Google Maps API key is configured -->
<?php if(env('GOOGLE_MAPS_API_KEY') && env('GOOGLE_MAPS_API_KEY') !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE'): ?>
    <!-- Google Maps JavaScript API -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(env('GOOGLE_MAPS_API_KEY')); ?>&callback=initDetailMap&libraries=geometry"
            onerror="handleDetailMapError()"></script>
<?php else: ?>
    <!-- No valid API key, show fallback immediately -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            showDetailMapError();
        });
    </script>
<?php endif; ?>

<script>
let detailMap;
let detailMarker;

// Error handling for Google Maps API
function handleDetailMapError() {
    console.error('Google Maps API failed to load');
    showDetailMapError();
}

function showDetailMapError() {
    const mapContainer = document.getElementById('detail-map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div class="flex items-center justify-center h-full bg-blue-50 border-2 border-blue-200 rounded-lg">
                <div class="text-center p-6">
                    <svg class="w-16 h-16 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-blue-800 mb-3">📍 Lokasi Laporan</h3>
                    <p class="text-blue-600 text-sm mb-4 max-w-sm mx-auto">
                        Peta interaktif belum tersedia, tetapi koordinat lokasi dapat digunakan untuk navigasi.
                    </p>
                    <div class="bg-white rounded-lg p-4 mb-4 border border-blue-200">
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-gray-600 font-medium">Latitude</div>
                                <div class="font-mono text-blue-800"><?php echo e(number_format($laporan->latitude, 6)); ?></div>
                            </div>
                            <div class="text-center">
                                <div class="text-gray-600 font-medium">Longitude</div>
                                <div class="font-mono text-blue-800"><?php echo e(number_format($laporan->longitude, 6)); ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                            <a href="https://www.google.com/maps/dir/?api=1&destination=<?php echo e($laporan->latitude); ?>,<?php echo e($laporan->longitude); ?>"
                               target="_blank"
                               class="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium transition">
                                🧭 Navigasi ke Lokasi
                            </a>
                        <?php endif; ?>
                        <a href="https://www.google.com/maps/search/?api=1&query=<?php echo e($laporan->latitude); ?>,<?php echo e($laporan->longitude); ?>"
                           target="_blank"
                           class="block w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm font-medium transition">
                            🗺️ Lihat di Google Maps
                        </a>
                    </div>
                </div>
            </div>
        `;
    }
}

function initDetailMap() {
    try {
        // Check if Google Maps is available
        if (typeof google === 'undefined' || !google.maps) {
            throw new Error('Google Maps API not loaded');
        }

        // Koordinat laporan
        const reportLocation = {
            lat: <?php echo e($laporan->latitude); ?>,
            lng: <?php echo e($laporan->longitude); ?>

        };

        // Initialize map
        detailMap = new google.maps.Map(document.getElementById('detail-map'), {
            zoom: 16,
            center: reportLocation,
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            zoomControl: true,
            mapTypeId: google.maps.MapTypeId.ROADMAP
        });
    } catch (error) {
        console.error('Error initializing detail map:', error);
        showDetailMapError();
        return;
    }

    // Add marker for report location
    detailMarker = new google.maps.Marker({
        position: reportLocation,
        map: detailMap,
        title: 'Lokasi Laporan: <?php echo e($laporan->judul); ?>',
        animation: google.maps.Animation.DROP,
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="#dc2626">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(40, 40),
            anchor: new google.maps.Point(20, 40)
        }
    });

    // Info window with report details
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="max-width: 250px; padding: 10px;">
                <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: bold; color: #1f2937;">
                    📍 <?php echo e($laporan->judul); ?>

                </h3>
                <div style="font-size: 14px; color: #4b5563; line-height: 1.4;">
                    <p style="margin: 4px 0;"><strong>Alamat:</strong> <?php echo e($laporan->alamat ?? 'Tidak ada alamat'); ?></p>
                    <p style="margin: 4px 0;"><strong>RT/RW:</strong> <?php echo e($laporan->rt ?? '-'); ?>/<?php echo e($laporan->rw ?? '-'); ?></p>
                    <p style="margin: 4px 0;"><strong>Status:</strong>
                        <span style="padding: 2px 6px; border-radius: 4px; font-size: 12px; font-weight: bold; color: white;
                            background-color: <?php echo e($laporan->status == 'belum_diproses' ? '#6b7280' : ($laporan->status == 'diproses' ? '#eab308' : '#16a34a')); ?>;">
                            <?php echo e(ucfirst($laporan->status)); ?>

                        </span>
                    </p>
                    <p style="margin: 8px 0 4px 0; font-size: 12px; color: #6b7280;">
                        Koordinat: <?php echo e(number_format($laporan->latitude, 6)); ?>, <?php echo e(number_format($laporan->longitude, 6)); ?>

                    </p>
                    <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                        <div style="margin-top: 10px; text-align: center;">
                            <a href="https://www.google.com/maps/dir/?api=1&destination=<?php echo e($laporan->latitude); ?>,<?php echo e($laporan->longitude); ?>"
                               target="_blank"
                               style="display: inline-block; padding: 6px 12px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 6px; font-size: 12px; font-weight: bold;">
                                🧭 Navigasi ke Lokasi
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        `
    });

    // Show info window when marker is clicked
    detailMarker.addListener('click', () => {
        infoWindow.open(detailMap, detailMarker);
    });

    // Auto-open info window after a short delay
    setTimeout(() => {
        infoWindow.open(detailMap, detailMarker);
    }, 1000);
}

// Handle map resize when container changes
window.addEventListener('resize', () => {
    if (detailMap) {
        google.maps.event.trigger(detailMap, 'resize');
        detailMap.setCenter({
            lat: <?php echo e($laporan->latitude); ?>,
            lng: <?php echo e($laporan->longitude); ?>

        });
    }
});
</script>
<?php $__env->stopSection(); ?>
<?php endif; ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\LaporinAja.github.io\resources\views/laporan/show.blade.php ENDPATH**/ ?>
<?php $__env->startSection('title', 'Daftar Laporan'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto">
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">Daftar Laporan</h1>

        <!-- Back to Dashboard Button -->
        <?php if(auth()->user()->role === 'petugas'): ?>
            <a href="<?php echo e(route('dashboard.petugas')); ?>"
               class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg shadow transition duration-200 flex items-center justify-center text-sm sm:text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span class="hidden sm:inline">Kembali ke Dashboard</span>
                <span class="sm:hidden">Dashboard</span>
            </a>
        <?php elseif(auth()->user()->role === 'admin'): ?>
            <a href="<?php echo e(route('admin.dashboard')); ?>"
               class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg shadow transition duration-200 flex items-center justify-center text-sm sm:text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span class="hidden sm:inline">Kembali ke Dashboard</span>
                <span class="sm:hidden">Dashboard</span>
            </a>
        <?php elseif(auth()->user()->role === 'user'): ?>
            <a href="<?php echo e(route('user.dashboard')); ?>"
               class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg shadow transition duration-200 flex items-center justify-center text-sm sm:text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span class="hidden sm:inline">Kembali ke Dashboard</span>
                <span class="sm:hidden">Dashboard</span>
            </a>
        <?php endif; ?>
    </div>

    <!-- Filter Status dan Prioritas -->
    <div class="mb-6 bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-xl shadow-lg">
        <!-- Filter Status -->
        <div class="mb-4">
            <h3 class="text-sm sm:text-base font-medium text-gray-700 dark:text-gray-300 mb-3">Filter Status:</h3>
            <div class="flex flex-wrap gap-2">
                <?php if(auth()->user()->role === 'user'): ?>
                    <a href="<?php echo e(route('user.laporan.index')); ?>"
                       class="px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors <?php echo e(!request('status') ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'); ?>">
                        📋 Semua
                    </a>
                    <a href="<?php echo e(route('user.laporan.index', ['status' => 'belum_diproses'])); ?>"
                       class="px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors <?php echo e(request('status') == 'belum_diproses' ? 'bg-red-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'); ?>">
                        ⏳ Belum Diproses
                    </a>
            <a href="<?php echo e(route('user.laporan.index', ['status' => 'diproses'])); ?>"
               class="px-3 py-1 rounded text-sm <?php echo e(request('status') == 'diproses' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                Diproses
            </a>
            <a href="<?php echo e(route('user.laporan.index', ['status' => 'selesai'])); ?>"
               class="px-3 py-1 rounded text-sm <?php echo e(request('status') == 'selesai' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                Selesai
            </a>
        <?php else: ?>
            <a href="<?php echo e(route('laporan.index')); ?>"
               class="px-3 py-1 rounded text-sm <?php echo e(!request('status') ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                Semua
            </a>
            <a href="<?php echo e(route('laporan.index', ['status' => 'belum_diproses'])); ?>"
               class="px-3 py-1 rounded text-sm <?php echo e(request('status') == 'belum_diproses' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                Belum Diproses
            </a>
            <a href="<?php echo e(route('laporan.index', ['status' => 'diproses'])); ?>"
               class="px-3 py-1 rounded text-sm <?php echo e(request('status') == 'diproses' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                Diproses
            </a>
            <a href="<?php echo e(route('laporan.index', ['status' => 'selesai'])); ?>"
               class="px-3 py-1 rounded text-sm <?php echo e(request('status') == 'selesai' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                Selesai
            </a>
        <?php endif; ?>
    </div>

    <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
    <!-- Filter Prioritas -->
    <div class="flex flex-wrap gap-2">
        <span class="text-sm font-medium text-gray-700 self-center">Prioritas:</span>
        <a href="<?php echo e(route('laporan.index', array_filter(['status' => request('status')]))); ?>"
           class="px-3 py-1 rounded text-sm <?php echo e(!request('prioritas') ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
            Semua
        </a>
        <a href="<?php echo e(route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'tinggi']))); ?>"
           class="px-3 py-1 rounded text-sm <?php echo e(request('prioritas') == 'tinggi' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
            🔴 Tinggi
        </a>
        <a href="<?php echo e(route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'sedang']))); ?>"
           class="px-3 py-1 rounded text-sm <?php echo e(request('prioritas') == 'sedang' ? 'bg-yellow-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
            🟡 Sedang
        </a>
        <a href="<?php echo e(route('laporan.index', array_filter(['status' => request('status'), 'prioritas' => 'rendah']))); ?>"
           class="px-3 py-1 rounded text-sm <?php echo e(request('prioritas') == 'rendah' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
            🟢 Rendah
        </a>
    </div>
    <?php endif; ?>
</div>

    <!-- Desktop Table View (hidden on mobile) -->
    <div class="hidden lg:block bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="bg-blue-600 dark:bg-blue-700 text-white">
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">#</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Judul</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Kategori</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Lokasi</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Prioritas</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                        <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">Pelapor</th>
                        <th class="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">Aksi</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
            <?php $__empty_1 = true; $__currentLoopData = $laporans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $laporan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <?php echo e($key + 1 + ($laporans->currentPage() - 1) * $laporans->perPage()); ?>

                </td>
                <td class="px-4 py-4">
                    <div class="text-sm font-medium text-gray-900"><?php echo e($laporan->judul); ?></div>
                    <div class="text-sm text-gray-500"><?php echo e(Str::limit($laporan->isi, 50)); ?></div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        <?php echo e($laporan->kategori ?? 'Tidak ada kategori'); ?>

                    </span>
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm text-gray-900">
                        <?php if($laporan->alamat): ?>
                            <div class="font-medium"><?php echo e(Str::limit($laporan->alamat, 30)); ?></div>
                            <div class="text-gray-500">RT <?php echo e($laporan->rt ?? '-'); ?> / RW <?php echo e($laporan->rw ?? '-'); ?></div>
                        <?php else: ?>
                            <span class="text-gray-400 italic">Alamat tidak tersedia</span>
                        <?php endif; ?>
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                        <!-- Form untuk mengubah prioritas -->
                        <form action="<?php echo e(route('laporan.prioritas', $laporan->id)); ?>" method="POST" class="inline-block">
                            <?php echo csrf_field(); ?>
                            <select name="prioritas" onchange="this.form.submit()"
                                    class="text-xs font-semibold rounded-full border-0 focus:ring-2 focus:ring-blue-500
                                    <?php if($laporan->prioritas == 'tinggi'): ?> bg-red-500 text-white
                                    <?php elseif($laporan->prioritas == 'sedang'): ?> bg-yellow-500 text-white
                                    <?php else: ?> bg-green-500 text-white <?php endif; ?>">
                                <option value="rendah" <?php echo e($laporan->prioritas == 'rendah' ? 'selected' : ''); ?>>🟢 Rendah</option>
                                <option value="sedang" <?php echo e($laporan->prioritas == 'sedang' ? 'selected' : ''); ?>>🟡 Sedang</option>
                                <option value="tinggi" <?php echo e($laporan->prioritas == 'tinggi' ? 'selected' : ''); ?>>🔴 Tinggi</option>
                            </select>
                        </form>
                    <?php else: ?>
                        <!-- Tampilan read-only untuk user -->
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            <?php if($laporan->prioritas == 'tinggi'): ?> bg-red-500 text-white
                            <?php elseif($laporan->prioritas == 'sedang'): ?> bg-yellow-500 text-white
                            <?php else: ?> bg-green-500 text-white <?php endif; ?>">
                            <?php if($laporan->prioritas == 'tinggi'): ?> 🔴 Tinggi
                            <?php elseif($laporan->prioritas == 'sedang'): ?> 🟡 Sedang
                            <?php else: ?> 🟢 Rendah <?php endif; ?>
                        </span>
                    <?php endif; ?>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <?php if($laporan->status == 'belum_diproses'): ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-600 text-white">⏳ Belum Diproses</span>
                    <?php elseif($laporan->status == 'diproses'): ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-600 text-white">🔄 Diproses</span>
                    <?php else: ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-600 text-white">✅ Selesai</span>
                    <?php endif; ?>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8">
                            <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-white font-medium text-xs">
                                    <?php echo e(strtoupper(substr($laporan->user->name, 0, 1))); ?>

                                </span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-900"><?php echo e($laporan->user->name); ?></div>
                        </div>
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div class="flex items-center justify-center space-x-2">
                        <a href="<?php echo e(route('laporan.show', $laporan->id)); ?>"
                           class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition duration-200">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Lihat
                        </a>

                        <?php if($laporan->status == 'belum_diproses'): ?>
                            <a href="<?php echo e(route('laporan.proses', $laporan->id)); ?>"
                               class="inline-flex items-center px-2 py-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Proses
                            </a>
                        <?php elseif($laporan->status == 'diproses'): ?>
                            <a href="<?php echo e(route('laporan.selesai', $laporan->id)); ?>"
                               class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Selesai
                            </a>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <tr>
                <td colspan="8" class="px-4 py-8 text-center">
                    <div class="flex flex-col items-center">
                        <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500 text-lg font-medium">Belum ada laporan.</p>
                    </div>
                </td>
            </tr>
            <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Mobile Card View (visible on mobile) -->
    <div class="lg:hidden space-y-4">
        <?php $__empty_1 = true; $__currentLoopData = $laporans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $laporan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-600">
                <!-- Header with number and status -->
                <div class="flex justify-between items-start mb-3">
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        #<?php echo e($key + 1 + ($laporans->currentPage() - 1) * $laporans->perPage()); ?>

                    </span>
                    <div class="flex flex-col items-end gap-1">
                        <?php if($laporan->status == 'belum_diproses'): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-600 text-white">⏳ Belum Diproses</span>
                        <?php elseif($laporan->status == 'diproses'): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-600 text-white">🔄 Diproses</span>
                        <?php else: ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-600 text-white">✅ Selesai</span>
                        <?php endif; ?>

                        <!-- Priority -->
                        <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                            <form action="<?php echo e(route('laporan.prioritas', $laporan->id)); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <select name="prioritas" onchange="this.form.submit()"
                                        class="text-xs font-semibold rounded-full border-0 focus:ring-2 focus:ring-blue-500 px-2 py-1
                                        <?php if($laporan->prioritas == 'tinggi'): ?> bg-red-500 text-white
                                        <?php elseif($laporan->prioritas == 'sedang'): ?> bg-yellow-500 text-white
                                        <?php else: ?> bg-green-500 text-white <?php endif; ?>">
                                    <option value="rendah" <?php echo e($laporan->prioritas == 'rendah' ? 'selected' : ''); ?>>🟢 Rendah</option>
                                    <option value="sedang" <?php echo e($laporan->prioritas == 'sedang' ? 'selected' : ''); ?>>🟡 Sedang</option>
                                    <option value="tinggi" <?php echo e($laporan->prioritas == 'tinggi' ? 'selected' : ''); ?>>🔴 Tinggi</option>
                                </select>
                            </form>
                        <?php else: ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                <?php if($laporan->prioritas == 'tinggi'): ?> bg-red-500 text-white
                                <?php elseif($laporan->prioritas == 'sedang'): ?> bg-yellow-500 text-white
                                <?php else: ?> bg-green-500 text-white <?php endif; ?>">
                                <?php if($laporan->prioritas == 'tinggi'): ?> 🔴 Tinggi
                                <?php elseif($laporan->prioritas == 'sedang'): ?> 🟡 Sedang
                                <?php else: ?> 🟢 Rendah <?php endif; ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Title and description -->
                <div class="mb-3">
                    <h3 class="font-semibold text-gray-900 dark:text-gray-100 text-sm mb-1"><?php echo e($laporan->judul); ?></h3>
                    <p class="text-xs text-gray-600 dark:text-gray-400 line-clamp-2"><?php echo e(Str::limit($laporan->isi, 80)); ?></p>
                </div>

                <!-- Category and Location -->
                <div class="grid grid-cols-1 gap-2 mb-3">
                    <div class="flex items-center">
                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">📂</span>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                            <?php echo e($laporan->kategori ?? 'Tidak ada kategori'); ?>

                        </span>
                    </div>
                    <div class="flex items-start">
                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2 mt-0.5">📍</span>
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                            <?php if($laporan->alamat): ?>
                                <div class="font-medium"><?php echo e(Str::limit($laporan->alamat, 40)); ?></div>
                                <div class="text-gray-500 dark:text-gray-500">RT <?php echo e($laporan->rt ?? '-'); ?> / RW <?php echo e($laporan->rw ?? '-'); ?></div>
                            <?php else: ?>
                                <span class="text-gray-400 italic">Alamat tidak tersedia</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Reporter -->
                <div class="flex items-center mb-3">
                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">👤</span>
                    <span class="text-xs text-gray-600 dark:text-gray-400"><?php echo e($laporan->user->nama ?? 'Tidak diketahui'); ?></span>
                </div>

                <!-- Actions -->
                <div class="flex flex-wrap gap-2 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <a href="<?php echo e(route('laporan.show', $laporan->id)); ?>"
                       class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition duration-200">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Lihat Detail
                    </a>

                    <?php if(in_array(auth()->user()->role, ['admin', 'petugas'])): ?>
                        <?php if($laporan->status == 'belum_diproses'): ?>
                            <a href="<?php echo e(route('laporan.proses', $laporan->id)); ?>"
                               class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Proses
                            </a>
                        <?php elseif($laporan->status == 'diproses'): ?>
                            <a href="<?php echo e(route('laporan.selesai', $laporan->id)); ?>"
                               class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Selesai
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400 text-lg font-medium mb-2">Belum ada laporan</p>
                    <p class="text-gray-400 dark:text-gray-500 text-sm">Laporan akan muncul di sini setelah dibuat</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        <?php echo e($laporans->links()); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\LaporinAja.github.io\resources\views/laporan/index.blade.php ENDPATH**/ ?>
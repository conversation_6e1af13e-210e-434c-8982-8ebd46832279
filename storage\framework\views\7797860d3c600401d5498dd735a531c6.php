<?php $__env->startSection('title', $title); ?>

<?php $__env->startSection('content'); ?>

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Selamat Datang, <?php echo e(auth()->user()->name); ?></h1>
        <a href="<?php echo e(route('lapor.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow">
            + Buat Laporan
        </a>
    </div>

    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">

        <a href="<?php echo e(route('user.laporan.index')); ?>"
            class="block p-8 min-h-[160px] bg-blue-100 rounded-2xl shadow hover:shadow-lg transition transform hover:scale-105 flex flex-col justify-center items-center">

            <div class="text-4xl mb-4">📝</div>
            <h2 class="text-2xl font-bold text-blue-800 mb-2">Total Laporan</h2>
            <p class="text-gray-700 text-lg"><?php echo e($totalLaporan); ?> laporan yang Anda kirim</p>
        </a>

        <a href="<?php echo e(route('user.laporan.index', ['status' => 'diproses'])); ?>"
            class="block p-8 min-h-[160px] bg-yellow-100 rounded-2xl shadow hover:shadow-lg transition transform hover:scale-105 flex flex-col justify-center items-center">

            <div class="text-4xl mb-4">⏳</div>
            <h2 class="text-xl font-bold text-yellow-800 mb-2">Sedang Diproses</h2>
            <p class="text-gray-700 text-lg"><?php echo e($laporanDiproses); ?> laporan diproses</p>
        </a>

        <a href="<?php echo e(route('user.laporan.index', ['status' => 'selesai'])); ?>"
            class="block p-8 min-h-[160px] bg-green-100 rounded-2xl shadow hover:shadow-lg transition transform hover:scale-105 flex flex-col justify-center items-center">

            <div class="text-4xl mb-4">✅</div>
            <h2 class="text-xl font-bold text-green-800 mb-2">Selesai</h2>
            <p class="text-gray-700 text-lg"><?php echo e($laporanSelesai); ?> laporan selesai</p>
        </a>

    </div>


    
    <div>
        <h2 class="text-xl font-semibold mb-4">Laporan Saya</h2>
        <?php $__empty_1 = true; $__currentLoopData = $laporans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $laporan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="p-4 mb-4 border rounded-xl shadow hover:shadow-md">
                <h3 class="font-bold text-lg mb-1"><?php echo e($laporan->judul); ?></h3>
                <p class="text-gray-700 mb-2"><?php echo e($laporan->isi); ?></p>
                <p>Status:
                    <?php if($laporan->status == 'belum_diproses'): ?>
                        <span class="text-red-500 font-semibold">Belum Diproses</span>
                    <?php elseif($laporan->status == 'diproses'): ?>
                        <span class="text-yellow-500 font-semibold">Sedang Diproses</span>
                    <?php elseif($laporan->status == 'selesai'): ?>
                        <span class="text-green-500 font-semibold">Selesai</span>
                    <?php endif; ?>
                </p>

                <?php if($laporan->bukti): ?>
                    <div class="mt-2">
                        <p class="font-semibold">Bukti Laporan:</p>
                        <img src="<?php echo e(asset('storage/' . $laporan->bukti)); ?>" class="w-48 mt-2 rounded">
                        <a href="<?php echo e(asset('storage/' . $laporan->bukti)); ?>" download
                            class="block text-blue-600 mt-2">Download Bukti</a>
                    </div>
                <?php endif; ?>


                <a href="<?php echo e(route('user.laporan.show', $laporan->id)); ?>"
                    class="text-blue-600 mt-3 inline-block hover:underline">Lihat Detail</a>
            </div>
            
            
            <form action="<?php echo e(route('feedback.store')); ?>" method="POST"
                class="w-full max-w-3xl bg-white p-6 rounded-lg shadow space-y-4">
                <?php echo csrf_field(); ?>

                <h2 class="text-xl font-bold text-gray-800">Beri Feedback Anda</h2>

                <div>
                    <label for="pesan" class="block text-gray-700 font-medium mb-1">Pesan:</label>
                    <textarea name="pesan" id="pesan"
                        class="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        style="height: 80px;" placeholder="Tulis pesan, saran, atau kritik Anda di sini..."></textarea>

                </div>

                <div>
                    <label for="rating" class="block text-gray-700 font-medium mb-1">Rating:</label>
                    <select name="rating" id="rating" required
                        class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Pilih Rating</option>
                        <option value="1">⭐</option>
                        <option value="2">⭐⭐</option>
                        <option value="3">⭐⭐⭐</option>
                        <option value="4">⭐⭐⭐⭐</option>
                        <option value="5">⭐⭐⭐⭐⭐</option>
                    </select>
                </div>

                <button type="submit"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg shadow transition duration-200">
                    Kirim Feedback
                </button>
            </form>


        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <p class="text-gray-500">Anda belum pernah mengirim laporan.</p>
        <?php endif; ?>
    </div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\LaporinAja.github.io\resources\views/dashboard/user.blade.php ENDPATH**/ ?>